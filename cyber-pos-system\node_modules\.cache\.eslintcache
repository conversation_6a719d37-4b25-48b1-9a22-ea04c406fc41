[{"E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js": "1", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx": "2", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx": "3", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx": "4", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx": "5", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx": "6", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx": "7", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx": "8", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx": "9", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx": "10", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx": "11", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx": "12", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx": "13", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts": "14", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts": "15", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx": "16", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts": "17", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx": "18", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx": "19", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx": "20", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts": "21"}, {"size": 362, "mtime": 1751001516282, "results": "22", "hashOfConfig": "23"}, {"size": 550, "mtime": 1751002257124, "results": "24", "hashOfConfig": "23"}, {"size": 2402, "mtime": 1751003134738, "results": "25", "hashOfConfig": "23"}, {"size": 5912, "mtime": 1751002889593, "results": "26", "hashOfConfig": "23"}, {"size": 6144, "mtime": 1751003013744, "results": "27", "hashOfConfig": "23"}, {"size": 5937, "mtime": 1751001851404, "results": "28", "hashOfConfig": "23"}, {"size": 1474, "mtime": 1751001862282, "results": "29", "hashOfConfig": "23"}, {"size": 1487, "mtime": 1751001889627, "results": "30", "hashOfConfig": "23"}, {"size": 7092, "mtime": 1751003059724, "results": "31", "hashOfConfig": "23"}, {"size": 1479, "mtime": 1751001880929, "results": "32", "hashOfConfig": "23"}, {"size": 6483, "mtime": 1751001825178, "results": "33", "hashOfConfig": "23"}, {"size": 13480, "mtime": 1751003649429, "results": "34", "hashOfConfig": "23"}, {"size": 665, "mtime": 1751001779724, "results": "35", "hashOfConfig": "23"}, {"size": 2499, "mtime": 1751003112125, "results": "36", "hashOfConfig": "23"}, {"size": 7270, "mtime": 1751002967950, "results": "37", "hashOfConfig": "23"}, {"size": 12716, "mtime": 1751002934527, "results": "38", "hashOfConfig": "23"}, {"size": 4406, "mtime": 1751003274192, "results": "39", "hashOfConfig": "23"}, {"size": 9054, "mtime": 1751003411814, "results": "40", "hashOfConfig": "23"}, {"size": 3964, "mtime": 1751003442458, "results": "41", "hashOfConfig": "23"}, {"size": 8498, "mtime": 1751003577995, "results": "42", "hashOfConfig": "23"}, {"size": 5162, "mtime": 1751003518513, "results": "43", "hashOfConfig": "23"}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "do8dzb", {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx", ["107"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx", ["108"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx", ["109", "110", "111", "112"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts", ["113", "114"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts", ["115", "116", "117"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx", ["118", "119"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts", ["120", "121"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx", ["122"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts", [], [], {"ruleId": "123", "severity": 1, "message": "124", "line": 17, "column": 7, "nodeType": "125", "messageId": "126", "endLine": 17, "endColumn": 62}, {"ruleId": "123", "severity": 1, "message": "127", "line": 16, "column": 3, "nodeType": "125", "messageId": "126", "endLine": 16, "endColumn": 8}, {"ruleId": "123", "severity": 1, "message": "128", "line": 9, "column": 3, "nodeType": "125", "messageId": "126", "endLine": 9, "endColumn": 13}, {"ruleId": "123", "severity": 1, "message": "129", "line": 10, "column": 3, "nodeType": "125", "messageId": "126", "endLine": 10, "endColumn": 6}, {"ruleId": "123", "severity": 1, "message": "130", "line": 14, "column": 3, "nodeType": "125", "messageId": "126", "endLine": 14, "endColumn": 4}, {"ruleId": "123", "severity": 1, "message": "131", "line": 15, "column": 3, "nodeType": "125", "messageId": "126", "endLine": 15, "endColumn": 7}, {"ruleId": "123", "severity": 1, "message": "132", "line": 7, "column": 3, "nodeType": "125", "messageId": "126", "endLine": 7, "endColumn": 27}, {"ruleId": "123", "severity": 1, "message": "133", "line": 10, "column": 22, "nodeType": "125", "messageId": "126", "endLine": 10, "endColumn": 44}, {"ruleId": "123", "severity": 1, "message": "134", "line": 13, "column": 10, "nodeType": "125", "messageId": "126", "endLine": 13, "endColumn": 14}, {"ruleId": "123", "severity": 1, "message": "135", "line": 13, "column": 16, "nodeType": "125", "messageId": "126", "endLine": 13, "endColumn": 23}, {"ruleId": "123", "severity": 1, "message": "136", "line": 13, "column": 25, "nodeType": "125", "messageId": "126", "endLine": 13, "endColumn": 32}, {"ruleId": "123", "severity": 1, "message": "137", "line": 8, "column": 3, "nodeType": "125", "messageId": "126", "endLine": 8, "endColumn": 9}, {"ruleId": "138", "severity": 1, "message": "139", "line": 34, "column": 6, "nodeType": "140", "endLine": 34, "endColumn": 8, "suggestions": "141"}, {"ruleId": "123", "severity": 1, "message": "142", "line": 5, "column": 3, "nodeType": "125", "messageId": "126", "endLine": 5, "endColumn": 10}, {"ruleId": "123", "severity": 1, "message": "127", "line": 11, "column": 3, "nodeType": "125", "messageId": "126", "endLine": 11, "endColumn": 8}, {"ruleId": "123", "severity": 1, "message": "129", "line": 2, "column": 25, "nodeType": "125", "messageId": "126", "endLine": 2, "endColumn": 28}, "@typescript-eslint/no-unused-vars", "'ProtectedRoute' is assigned a value but never used.", "Identifier", "unusedVar", "'where' is defined but never used.", "'DollarSign' is defined but never used.", "'Tag' is defined but never used.", "'X' is defined but never used.", "'Save' is defined but never used.", "'connectFirestoreEmulator' is defined but never used.", "'connectStorageEmulator' is defined but never used.", "'User' is defined but never used.", "'Service' is defined but never used.", "'Product' is defined but never used.", "'Trash2' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["143"], "'getDocs' is defined but never used.", {"desc": "144", "fix": "145"}, "Update the dependencies array to be: [loadUsers]", {"range": "146", "text": "147"}, [865, 867], "[loadUsers]"]