{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M10 10 7 7\",\n  key: \"zp14k7\"\n}], [\"path\", {\n  d: \"m10 14-3 3\",\n  key: \"1jrpxk\"\n}], [\"path\", {\n  d: \"m14 10 3-3\",\n  key: \"7tigam\"\n}], [\"path\", {\n  d: \"m14 14 3 3\",\n  key: \"vm23p3\"\n}], [\"path\", {\n  d: \"M14.205 4.139a4 4 0 1 1 5.439 5.863\",\n  key: \"1tm5p2\"\n}], [\"path\", {\n  d: \"M19.637 14a4 4 0 1 1-5.432 5.868\",\n  key: \"16egi2\"\n}], [\"path\", {\n  d: \"M4.367 10a4 4 0 1 1 5.438-5.862\",\n  key: \"1wta6a\"\n}], [\"path\", {\n  d: \"M9.795 19.862a4 4 0 1 1-5.429-5.873\",\n  key: \"q39hpv\"\n}], [\"rect\", {\n  x: \"10\",\n  y: \"8\",\n  width: \"4\",\n  height: \"8\",\n  rx: \"1\",\n  key: \"phrjt1\"\n}]];\nconst Drone = createLucideIcon(\"drone\", __iconNode);\nexport { __iconNode, Drone as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "x", "y", "width", "height", "rx", "Drone", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\drone.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M10 10 7 7', key: 'zp14k7' }],\n  ['path', { d: 'm10 14-3 3', key: '1jrpxk' }],\n  ['path', { d: 'm14 10 3-3', key: '7tigam' }],\n  ['path', { d: 'm14 14 3 3', key: 'vm23p3' }],\n  ['path', { d: 'M14.205 4.139a4 4 0 1 1 5.439 5.863', key: '1tm5p2' }],\n  ['path', { d: 'M19.637 14a4 4 0 1 1-5.432 5.868', key: '16egi2' }],\n  ['path', { d: 'M4.367 10a4 4 0 1 1 5.438-5.862', key: '1wta6a' }],\n  ['path', { d: 'M9.795 19.862a4 4 0 1 1-5.429-5.873', key: 'q39hpv' }],\n  ['rect', { x: '10', y: '8', width: '4', height: '8', rx: '1', key: 'phrjt1' }],\n];\n\n/**\n * @component @name Drone\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMTAgNyA3IiAvPgogIDxwYXRoIGQ9Im0xMCAxNC0zIDMiIC8+CiAgPHBhdGggZD0ibTE0IDEwIDMtMyIgLz4KICA8cGF0aCBkPSJtMTQgMTQgMyAzIiAvPgogIDxwYXRoIGQ9Ik0xNC4yMDUgNC4xMzlhNCA0IDAgMSAxIDUuNDM5IDUuODYzIiAvPgogIDxwYXRoIGQ9Ik0xOS42MzcgMTRhNCA0IDAgMSAxLTUuNDMyIDUuODY4IiAvPgogIDxwYXRoIGQ9Ik00LjM2NyAxMGE0IDQgMCAxIDEgNS40MzgtNS44NjIiIC8+CiAgPHBhdGggZD0iTTkuNzk1IDE5Ljg2MmE0IDQgMCAxIDEtNS40MjktNS44NzMiIC8+CiAgPHJlY3QgeD0iMTAiIHk9IjgiIHdpZHRoPSI0IiBoZWlnaHQ9IjgiIHJ4PSIxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/drone\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Drone = createLucideIcon('drone', __iconNode);\n\nexport default Drone;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,MAAQ;EAAED,CAAA,EAAG,kCAAoC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjE,CAAC,MAAQ;EAAED,CAAA,EAAG,iCAAmC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAED,CAAA,EAAG,qCAAuC;EAAAC,GAAA,EAAK;AAAA,CAAU,GACpE,CAAC,QAAQ;EAAEC,CAAA,EAAG;EAAMC,CAAG;EAAKC,KAAO;EAAKC,MAAA,EAAQ,GAAK;EAAAC,EAAA,EAAI,GAAK;EAAAL,GAAA,EAAK;AAAU,GAC/E;AAaM,MAAAM,KAAA,GAAQC,gBAAiB,UAAST,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}