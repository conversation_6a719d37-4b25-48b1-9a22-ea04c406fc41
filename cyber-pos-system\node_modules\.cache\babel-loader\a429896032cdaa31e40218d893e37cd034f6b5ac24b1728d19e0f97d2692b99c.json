{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"circle\", {\n  cx: \"10\",\n  cy: \"7\",\n  r: \"4\",\n  key: \"e45bow\"\n}], [\"path\", {\n  d: \"M10.3 15H7a4 4 0 0 0-4 4v2\",\n  key: \"3bnktk\"\n}], [\"circle\", {\n  cx: \"17\",\n  cy: \"17\",\n  r: \"3\",\n  key: \"18b49y\"\n}], [\"path\", {\n  d: \"m21 21-1.9-1.9\",\n  key: \"1g2n9r\"\n}]];\nconst UserSearch = createLucideIcon(\"user-search\", __iconNode);\nexport { __iconNode, UserSearch as default };", "map": {"version": 3, "names": ["__iconNode", "cx", "cy", "r", "key", "d", "UserSearch", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\user-search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '10', cy: '7', r: '4', key: 'e45bow' }],\n  ['path', { d: 'M10.3 15H7a4 4 0 0 0-4 4v2', key: '3bnktk' }],\n  ['circle', { cx: '17', cy: '17', r: '3', key: '18b49y' }],\n  ['path', { d: 'm21 21-1.9-1.9', key: '1g2n9r' }],\n];\n\n/**\n * @component @name UserSearch\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMCIgY3k9IjciIHI9IjQiIC8+CiAgPHBhdGggZD0iTTEwLjMgMTVIN2E0IDQgMCAwIDAtNCA0djIiIC8+CiAgPGNpcmNsZSBjeD0iMTciIGN5PSIxNyIgcj0iMyIgLz4KICA8cGF0aCBkPSJtMjEgMjEtMS45LTEuOSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/user-search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst UserSearch = createLucideIcon('user-search', __iconNode);\n\nexport default UserSearch;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CAAC,MAAQ;EAAEC,CAAA,EAAG,4BAA8B;EAAAD,GAAA,EAAK;AAAA,CAAU,GAC3D,CAAC,QAAU;EAAEH,EAAI;EAAMC,EAAI;EAAMC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACxD,CAAC,MAAQ;EAAEC,CAAA,EAAG,gBAAkB;EAAAD,GAAA,EAAK;AAAU,GACjD;AAaM,MAAAE,UAAA,GAAaC,gBAAiB,gBAAeP,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}