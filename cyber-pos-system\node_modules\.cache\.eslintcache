[{"E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js": "1", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx": "2", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx": "3", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx": "4", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx": "5", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx": "6", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx": "7", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx": "8", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx": "9", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx": "10", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx": "11", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx": "12", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx": "13", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts": "14", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts": "15", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx": "16"}, {"size": 362, "mtime": 1751001516282, "results": "17", "hashOfConfig": "18"}, {"size": 550, "mtime": 1751002257124, "results": "19", "hashOfConfig": "18"}, {"size": 2402, "mtime": 1751003134738, "results": "20", "hashOfConfig": "18"}, {"size": 5912, "mtime": 1751002889593, "results": "21", "hashOfConfig": "18"}, {"size": 6144, "mtime": 1751003013744, "results": "22", "hashOfConfig": "18"}, {"size": 5937, "mtime": 1751001851404, "results": "23", "hashOfConfig": "18"}, {"size": 1474, "mtime": 1751001862282, "results": "24", "hashOfConfig": "18"}, {"size": 1487, "mtime": 1751001889627, "results": "25", "hashOfConfig": "18"}, {"size": 7092, "mtime": 1751003059724, "results": "26", "hashOfConfig": "18"}, {"size": 1479, "mtime": 1751001880929, "results": "27", "hashOfConfig": "18"}, {"size": 6483, "mtime": 1751001825178, "results": "28", "hashOfConfig": "18"}, {"size": 1478, "mtime": 1751001872000, "results": "29", "hashOfConfig": "18"}, {"size": 665, "mtime": 1751001779724, "results": "30", "hashOfConfig": "18"}, {"size": 2499, "mtime": 1751003112125, "results": "31", "hashOfConfig": "18"}, {"size": 7270, "mtime": 1751002967950, "results": "32", "hashOfConfig": "18"}, {"size": 12716, "mtime": 1751002934527, "results": "33", "hashOfConfig": "18"}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "do8dzb", {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx", ["82"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx", ["83"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts", ["84", "85"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts", ["86", "87", "88"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx", ["89", "90"], [], {"ruleId": "91", "severity": 1, "message": "92", "line": 17, "column": 7, "nodeType": "93", "messageId": "94", "endLine": 17, "endColumn": 62}, {"ruleId": "91", "severity": 1, "message": "95", "line": 16, "column": 3, "nodeType": "93", "messageId": "94", "endLine": 16, "endColumn": 8}, {"ruleId": "91", "severity": 1, "message": "96", "line": 7, "column": 3, "nodeType": "93", "messageId": "94", "endLine": 7, "endColumn": 27}, {"ruleId": "91", "severity": 1, "message": "97", "line": 10, "column": 22, "nodeType": "93", "messageId": "94", "endLine": 10, "endColumn": 44}, {"ruleId": "91", "severity": 1, "message": "98", "line": 13, "column": 10, "nodeType": "93", "messageId": "94", "endLine": 13, "endColumn": 14}, {"ruleId": "91", "severity": 1, "message": "99", "line": 13, "column": 16, "nodeType": "93", "messageId": "94", "endLine": 13, "endColumn": 23}, {"ruleId": "91", "severity": 1, "message": "100", "line": 13, "column": 25, "nodeType": "93", "messageId": "94", "endLine": 13, "endColumn": 32}, {"ruleId": "91", "severity": 1, "message": "101", "line": 8, "column": 3, "nodeType": "93", "messageId": "94", "endLine": 8, "endColumn": 9}, {"ruleId": "102", "severity": 1, "message": "103", "line": 34, "column": 6, "nodeType": "104", "endLine": 34, "endColumn": 8, "suggestions": "105"}, "@typescript-eslint/no-unused-vars", "'ProtectedRoute' is assigned a value but never used.", "Identifier", "unusedVar", "'where' is defined but never used.", "'connectFirestoreEmulator' is defined but never used.", "'connectStorageEmulator' is defined but never used.", "'User' is defined but never used.", "'Service' is defined but never used.", "'Product' is defined but never used.", "'Trash2' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["106"], {"desc": "107", "fix": "108"}, "Update the dependencies array to be: [loadUsers]", {"range": "109", "text": "110"}, [865, 867], "[loadUsers]"]