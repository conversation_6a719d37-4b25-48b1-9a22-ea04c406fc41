{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z\",\n  key: \"q3az6g\"\n}], [\"path\", {\n  d: \"M8 15h5\",\n  key: \"vxg57a\"\n}], [\"path\", {\n  d: \"M8 11h5a2 2 0 1 0 0-4h-3v10\",\n  key: \"1usi5u\"\n}]];\nconst ReceiptRussianRuble = createLucideIcon(\"receipt-russian-ruble\", __iconNode);\nexport { __iconNode, ReceiptRussianRuble as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "ReceiptRussianRuble", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\receipt-russian-ruble.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    { d: 'M4 2v20l2-1 2 1 2-1 2 1 2-1 2 1 2-1 2 1V2l-2 1-2-1-2 1-2-1-2 1-2-1-2 1Z', key: 'q3az6g' },\n  ],\n  ['path', { d: 'M8 15h5', key: 'vxg57a' }],\n  ['path', { d: 'M8 11h5a2 2 0 1 0 0-4h-3v10', key: '1usi5u' }],\n];\n\n/**\n * @component @name ReceiptRussianRuble\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAydjIwbDItMSAyIDEgMi0xIDIgMSAyLTEgMiAxIDItMSAyIDFWMmwtMiAxLTItMS0yIDEtMi0xLTIgMS0yLTEtMiAxWiIgLz4KICA8cGF0aCBkPSJNOCAxNWg1IiAvPgogIDxwYXRoIGQ9Ik04IDExaDVhMiAyIDAgMSAwIDAtNGgtM3YxMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/receipt-russian-ruble\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ReceiptRussianRuble = createLucideIcon('receipt-russian-ruble', __iconNode);\n\nexport default ReceiptRussianRuble;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EAAEC,CAAA,EAAG,yEAA2E;EAAAC,GAAA,EAAK;AAAS,EAChG,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,6BAA+B;EAAAC,GAAA,EAAK;AAAU,GAC9D;AAaM,MAAAC,mBAAA,GAAsBC,gBAAiB,0BAAyBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}