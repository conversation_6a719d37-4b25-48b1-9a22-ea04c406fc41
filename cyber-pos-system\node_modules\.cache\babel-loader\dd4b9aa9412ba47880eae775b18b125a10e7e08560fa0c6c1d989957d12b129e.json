{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M3 12h.01\",\n  key: \"nlz23k\"\n}], [\"path\", {\n  d: \"M3 18h.01\",\n  key: \"1tta3j\"\n}], [\"path\", {\n  d: \"M3 6h.01\",\n  key: \"1rqtza\"\n}], [\"path\", {\n  d: \"M8 12h13\",\n  key: \"1za7za\"\n}], [\"path\", {\n  d: \"M8 18h13\",\n  key: \"1lx6n3\"\n}], [\"path\", {\n  d: \"M8 6h13\",\n  key: \"ik3vkj\"\n}]];\nconst List = createLucideIcon(\"list\", __iconNode);\nexport { __iconNode, List as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "List", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\list.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12h.01', key: 'nlz23k' }],\n  ['path', { d: 'M3 18h.01', key: '1tta3j' }],\n  ['path', { d: 'M3 6h.01', key: '1rqtza' }],\n  ['path', { d: 'M8 12h13', key: '1za7za' }],\n  ['path', { d: 'M8 18h13', key: '1lx6n3' }],\n  ['path', { d: 'M8 6h13', key: 'ik3vkj' }],\n];\n\n/**\n * @component @name List\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmguMDEiIC8+CiAgPHBhdGggZD0iTTMgMThoLjAxIiAvPgogIDxwYXRoIGQ9Ik0zIDZoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEyaDEzIiAvPgogIDxwYXRoIGQ9Ik04IDE4aDEzIiAvPgogIDxwYXRoIGQ9Ik04IDZoMTMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/list\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst List = createLucideIcon('list', __iconNode);\n\nexport default List;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAU,GAC1C;AAaM,MAAAC,IAAA,GAAOC,gBAAiB,SAAQJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}