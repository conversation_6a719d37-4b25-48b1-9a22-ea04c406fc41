{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M12 2c1 3 2.5 3.5 3.5 4.5A5 5 0 0 1 17 10a5 5 0 1 1-10 0c0-.3 0-.6.1-.9a2 2 0 1 0 3.3-2C8 4.5 11 2 12 2Z\",\n  key: \"1ir223\"\n}], [\"path\", {\n  d: \"m5 22 14-4\",\n  key: \"1brv4h\"\n}], [\"path\", {\n  d: \"m5 18 14 4\",\n  key: \"lgyyje\"\n}]];\nconst FlameKindling = createLucideIcon(\"flame-kindling\", __iconNode);\nexport { __iconNode, FlameKindling as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "Flame<PERSON><PERSON>ling", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\flame-kindling.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M12 2c1 3 2.5 3.5 3.5 4.5A5 5 0 0 1 17 10a5 5 0 1 1-10 0c0-.3 0-.6.1-.9a2 2 0 1 0 3.3-2C8 4.5 11 2 12 2Z',\n      key: '1ir223',\n    },\n  ],\n  ['path', { d: 'm5 22 14-4', key: '1brv4h' }],\n  ['path', { d: 'm5 18 14 4', key: 'lgyyje' }],\n];\n\n/**\n * @component @name FlameKindling\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgMmMxIDMgMi41IDMuNSAzLjUgNC41QTUgNSAwIDAgMSAxNyAxMGE1IDUgMCAxIDEtMTAgMGMwLS4zIDAtLjYuMS0uOWEyIDIgMCAxIDAgMy4zLTJDOCA0LjUgMTEgMiAxMiAyWiIgLz4KICA8cGF0aCBkPSJtNSAyMiAxNC00IiAvPgogIDxwYXRoIGQ9Im01IDE4IDE0IDQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/flame-kindling\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst FlameKindling = createLucideIcon('flame-kindling', __iconNode);\n\nexport default FlameKindling;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AAAA,EAET,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAU,GAC7C;AAaM,MAAAC,aAAA,GAAgBC,gBAAiB,mBAAkBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}