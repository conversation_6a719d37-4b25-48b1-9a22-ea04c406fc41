[{"E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js": "1", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx": "2", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx": "3", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx": "4", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx": "5", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx": "6", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx": "7", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx": "8", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx": "9", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx": "10", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx": "11", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx": "12", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx": "13", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts": "14", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts": "15", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx": "16", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts": "17", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx": "18", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx": "19", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx": "20", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts": "21", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts": "22", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts": "23", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx": "24", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx": "25", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx": "26", "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx": "27"}, {"size": 362, "mtime": 1751001516282, "results": "28", "hashOfConfig": "29"}, {"size": 550, "mtime": 1751002257124, "results": "30", "hashOfConfig": "29"}, {"size": 2402, "mtime": 1751003134738, "results": "31", "hashOfConfig": "29"}, {"size": 5912, "mtime": 1751002889593, "results": "32", "hashOfConfig": "29"}, {"size": 6144, "mtime": 1751003013744, "results": "33", "hashOfConfig": "29"}, {"size": 5937, "mtime": 1751001851404, "results": "34", "hashOfConfig": "29"}, {"size": 8045, "mtime": 1751003816943, "results": "35", "hashOfConfig": "29"}, {"size": 1487, "mtime": 1751001889627, "results": "36", "hashOfConfig": "29"}, {"size": 7092, "mtime": 1751003059724, "results": "37", "hashOfConfig": "29"}, {"size": 1479, "mtime": 1751001880929, "results": "38", "hashOfConfig": "29"}, {"size": 6483, "mtime": 1751001825178, "results": "39", "hashOfConfig": "29"}, {"size": 13480, "mtime": 1751003649429, "results": "40", "hashOfConfig": "29"}, {"size": 665, "mtime": 1751001779724, "results": "41", "hashOfConfig": "29"}, {"size": 2499, "mtime": 1751003112125, "results": "42", "hashOfConfig": "29"}, {"size": 7270, "mtime": 1751002967950, "results": "43", "hashOfConfig": "29"}, {"size": 12716, "mtime": 1751002934527, "results": "44", "hashOfConfig": "29"}, {"size": 4406, "mtime": 1751003274192, "results": "45", "hashOfConfig": "29"}, {"size": 9054, "mtime": 1751003411814, "results": "46", "hashOfConfig": "29"}, {"size": 3964, "mtime": 1751003442458, "results": "47", "hashOfConfig": "29"}, {"size": 8498, "mtime": 1751003577995, "results": "48", "hashOfConfig": "29"}, {"size": 5162, "mtime": 1751003518513, "results": "49", "hashOfConfig": "29"}, {"size": 6079, "mtime": 1751003779872, "results": "50", "hashOfConfig": "29"}, {"size": 5107, "mtime": 1751003753867, "results": "51", "hashOfConfig": "29"}, {"size": 12775, "mtime": 1751004057085, "results": "52", "hashOfConfig": "29"}, {"size": 13401, "mtime": 1751003915007, "results": "53", "hashOfConfig": "29"}, {"size": 10910, "mtime": 1751003957303, "results": "54", "hashOfConfig": "29"}, {"size": 11914, "mtime": 1751004034012, "results": "55", "hashOfConfig": "29"}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "do8dzb", {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "122", "messages": "123", "suppressedMessages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "125", "messages": "126", "suppressedMessages": "127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "128", "messages": "129", "suppressedMessages": "130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "131", "messages": "132", "suppressedMessages": "133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "134", "messages": "135", "suppressedMessages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\reportWebVitals.js", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\index.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\App.tsx", ["137"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\contexts\\AuthContext.tsx", ["138"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\auth\\Login.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\dashboard\\Dashboard.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POS.tsx", ["139", "140", "141", "142", "143", "144", "145", "146"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\reports\\Reports.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\settings\\Settings.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\inventory\\Inventory.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\layout\\Layout.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\Services.tsx", ["147", "148", "149", "150"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\common\\LoadingSpinner.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\config\\firebase.ts", ["151", "152"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\seedData.ts", ["153", "154", "155"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\users\\UserManagement.tsx", ["156", "157"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useServices.ts", ["158", "159"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceModal.tsx", ["160"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceCategories.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\services\\ServiceStats.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\utils\\serviceUtils.ts", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useProducts.ts", ["161", "162"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\hooks\\useCart.ts", ["163", "164"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\POSCart.tsx", ["165", "166", "167"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ServiceSelector.tsx", ["168"], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\ProductSelector.tsx", [], [], "E:\\FX\\Cyber POS\\cyber-pos-system\\src\\components\\pos\\CheckoutModal.tsx", ["169"], [], {"ruleId": "170", "severity": 1, "message": "171", "line": 17, "column": 7, "nodeType": "172", "messageId": "173", "endLine": 17, "endColumn": 62}, {"ruleId": "170", "severity": 1, "message": "174", "line": 16, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 16, "endColumn": 8}, {"ruleId": "170", "severity": 1, "message": "175", "line": 10, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 10, "endColumn": 7}, {"ruleId": "170", "severity": 1, "message": "176", "line": 11, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 11, "endColumn": 13}, {"ruleId": "170", "severity": 1, "message": "177", "line": 12, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 12, "endColumn": 10}, {"ruleId": "170", "severity": 1, "message": "178", "line": 13, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 13, "endColumn": 9}, {"ruleId": "170", "severity": 1, "message": "179", "line": 14, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 14, "endColumn": 7}, {"ruleId": "170", "severity": 1, "message": "180", "line": 15, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 15, "endColumn": 8}, {"ruleId": "170", "severity": 1, "message": "181", "line": 21, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 21, "endColumn": 17}, {"ruleId": "170", "severity": 1, "message": "182", "line": 21, "column": 19, "nodeType": "172", "messageId": "173", "endLine": 21, "endColumn": 26}, {"ruleId": "170", "severity": 1, "message": "183", "line": 9, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 9, "endColumn": 13}, {"ruleId": "170", "severity": 1, "message": "184", "line": 10, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 10, "endColumn": 6}, {"ruleId": "170", "severity": 1, "message": "185", "line": 14, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 14, "endColumn": 4}, {"ruleId": "170", "severity": 1, "message": "186", "line": 15, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 15, "endColumn": 7}, {"ruleId": "170", "severity": 1, "message": "187", "line": 7, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 7, "endColumn": 27}, {"ruleId": "170", "severity": 1, "message": "188", "line": 10, "column": 22, "nodeType": "172", "messageId": "173", "endLine": 10, "endColumn": 44}, {"ruleId": "170", "severity": 1, "message": "175", "line": 13, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 13, "endColumn": 14}, {"ruleId": "170", "severity": 1, "message": "181", "line": 13, "column": 16, "nodeType": "172", "messageId": "173", "endLine": 13, "endColumn": 23}, {"ruleId": "170", "severity": 1, "message": "182", "line": 13, "column": 25, "nodeType": "172", "messageId": "173", "endLine": 13, "endColumn": 32}, {"ruleId": "170", "severity": 1, "message": "178", "line": 8, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 8, "endColumn": 9}, {"ruleId": "189", "severity": 1, "message": "190", "line": 34, "column": 6, "nodeType": "191", "endLine": 34, "endColumn": 8, "suggestions": "192"}, {"ruleId": "170", "severity": 1, "message": "193", "line": 5, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 5, "endColumn": 10}, {"ruleId": "170", "severity": 1, "message": "174", "line": 11, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 11, "endColumn": 8}, {"ruleId": "170", "severity": 1, "message": "184", "line": 2, "column": 25, "nodeType": "172", "messageId": "173", "endLine": 2, "endColumn": 28}, {"ruleId": "170", "severity": 1, "message": "193", "line": 5, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 5, "endColumn": 10}, {"ruleId": "170", "severity": 1, "message": "174", "line": 11, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 11, "endColumn": 8}, {"ruleId": "170", "severity": 1, "message": "194", "line": 3, "column": 10, "nodeType": "172", "messageId": "173", "endLine": 3, "endColumn": 31}, {"ruleId": "189", "severity": 1, "message": "195", "line": 87, "column": 6, "nodeType": "191", "endLine": 87, "endColumn": 8, "suggestions": "196"}, {"ruleId": "170", "severity": 1, "message": "183", "line": 8, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 8, "endColumn": 13}, {"ruleId": "170", "severity": 1, "message": "175", "line": 10, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 10, "endColumn": 7}, {"ruleId": "170", "severity": 1, "message": "176", "line": 11, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 11, "endColumn": 13}, {"ruleId": "170", "severity": 1, "message": "183", "line": 5, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 5, "endColumn": 13}, {"ruleId": "170", "severity": 1, "message": "197", "line": 9, "column": 3, "nodeType": "172", "messageId": "173", "endLine": 9, "endColumn": 10}, "@typescript-eslint/no-unused-vars", "'ProtectedRoute' is assigned a value but never used.", "Identifier", "unusedVar", "'where' is defined but never used.", "'User' is defined but never used.", "'CreditCard' is defined but never used.", "'Receipt' is defined but never used.", "'Trash2' is defined but never used.", "'Plus' is defined but never used.", "'Minus' is defined but never used.", "'Service' is defined but never used.", "'Product' is defined but never used.", "'DollarSign' is defined but never used.", "'Tag' is defined but never used.", "'X' is defined but never used.", "'Save' is defined but never used.", "'connectFirestoreEmulator' is defined but never used.", "'connectStorageEmulator' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["198"], "'getDocs' is defined but never used.", "'calculateServicePrice' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeFromCart'. Either include it or remove the dependency array.", ["199"], "'Printer' is defined but never used.", {"desc": "200", "fix": "201"}, {"desc": "202", "fix": "203"}, "Update the dependencies array to be: [loadUsers]", {"range": "204", "text": "205"}, "Update the dependencies array to be: [removeFromCart]", {"range": "206", "text": "207"}, [865, 867], "[loadUsers]", [2571, 2573], "[remove<PERSON><PERSON><PERSON><PERSON>]"]