{"ast": null, "code": "/**\n * @license lucide-react v0.523.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst __iconNode = [[\"path\", {\n  d: \"M2 22V12a10 10 0 1 1 20 0v10\",\n  key: \"o0fyp0\"\n}], [\"path\", {\n  d: \"M15 6.8v1.4a3 2.8 0 1 1-6 0V6.8\",\n  key: \"m8q3n9\"\n}], [\"path\", {\n  d: \"M10 15h.01\",\n  key: \"44in9x\"\n}], [\"path\", {\n  d: \"M14 15h.01\",\n  key: \"5mohn5\"\n}], [\"path\", {\n  d: \"M10 19a4 4 0 0 1-4-4v-3a6 6 0 1 1 12 0v3a4 4 0 0 1-4 4Z\",\n  key: \"hckbmu\"\n}], [\"path\", {\n  d: \"m9 19-2 3\",\n  key: \"iij7hm\"\n}], [\"path\", {\n  d: \"m15 19 2 3\",\n  key: \"npx8sa\"\n}]];\nconst TrainFrontTunnel = createLucideIcon(\"train-front-tunnel\", __iconNode);\nexport { __iconNode, TrainFrontTunnel as default };", "map": {"version": 3, "names": ["__iconNode", "d", "key", "TrainFrontTunnel", "createLucideIcon"], "sources": ["E:\\FX\\Cyber POS\\cyber-pos-system\\node_modules\\lucide-react\\src\\icons\\train-front-tunnel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M2 22V12a10 10 0 1 1 20 0v10', key: 'o0fyp0' }],\n  ['path', { d: 'M15 6.8v1.4a3 2.8 0 1 1-6 0V6.8', key: 'm8q3n9' }],\n  ['path', { d: 'M10 15h.01', key: '44in9x' }],\n  ['path', { d: 'M14 15h.01', key: '5mohn5' }],\n  ['path', { d: 'M10 19a4 4 0 0 1-4-4v-3a6 6 0 1 1 12 0v3a4 4 0 0 1-4 4Z', key: 'hckbmu' }],\n  ['path', { d: 'm9 19-2 3', key: 'iij7hm' }],\n  ['path', { d: 'm15 19 2 3', key: 'npx8sa' }],\n];\n\n/**\n * @component @name TrainFrontTunnel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMiAyMlYxMmExMCAxMCAwIDEgMSAyMCAwdjEwIiAvPgogIDxwYXRoIGQ9Ik0xNSA2Ljh2MS40YTMgMi44IDAgMSAxLTYgMFY2LjgiIC8+CiAgPHBhdGggZD0iTTEwIDE1aC4wMSIgLz4KICA8cGF0aCBkPSJNMTQgMTVoLjAxIiAvPgogIDxwYXRoIGQ9Ik0xMCAxOWE0IDQgMCAwIDEtNC00di0zYTYgNiAwIDEgMSAxMiAwdjNhNCA0IDAgMCAxLTQgNFoiIC8+CiAgPHBhdGggZD0ibTkgMTktMiAzIiAvPgogIDxwYXRoIGQ9Im0xNSAxOSAyIDMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/train-front-tunnel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrainFrontTunnel = createLucideIcon('train-front-tunnel', __iconNode);\n\nexport default TrainFrontTunnel;\n"], "mappings": ";;;;;;;;AAGO,MAAMA,UAAuB,IAClC,CAAC,MAAQ;EAAEC,CAAA,EAAG,8BAAgC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7D,CAAC,MAAQ;EAAED,CAAA,EAAG,iCAAmC;EAAAC,GAAA,EAAK;AAAA,CAAU,GAChE,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,yDAA2D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxF,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAU,GAC7C;AAaM,MAAAC,gBAAA,GAAmBC,gBAAiB,uBAAsBJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}